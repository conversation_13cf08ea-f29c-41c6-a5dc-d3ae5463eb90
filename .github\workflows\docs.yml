name: 📚 Docs

on:
  push:
    branches:
      - main

# Sets permissions of the GITHUB_TOKEN to allow deployment to GitHub Pages
permissions:
  actions: read
  pages: write
  id-token: write
  contents: read

# Allow only one concurrent deployment, skipping runs queued between the run in-progress and latest queued.
# However, do NOT cancel in-progress runs as we want to allow these production deployments to complete.
concurrency:
  group: pages
  cancel-in-progress: false

jobs:
  publish-docs:
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: windows-2022
    steps:
    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
      with:
        fetch-depth: 0 # avoid shallow clone so nbgv can do its work.
    - name: ⚙ Install prerequisites
      run: ./init.ps1 -UpgradePrerequisites

    - run: dotnet docfx docfx/docfx.json
      name: 📚 Generate documentation

    - name: Upload artifact
      uses: actions/upload-pages-artifact@56afc609e74202658d3ffba0e8f6dda462b719fa # v3
      with:
        path: docfx/_site

    - name: Deploy to GitHub Pages
      id: deployment
      uses: actions/deploy-pages@d6db90164ac5ed86f2b6aed7e0febac5b3c0c03e # v4
